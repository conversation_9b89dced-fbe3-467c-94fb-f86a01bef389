<?php

declare(strict_types=1);

use App\Http\Controllers\ResidentController;
use Illuminate\Support\Facades\Route;

Route::group(['prefix' => 'resident'], function () {
    Route::post('login', [ResidentController::class, 'login']);

    Route::middleware('auth:sanctum')->group(function () {
        Route::get('profile', [ResidentController::class, 'profile']);
        Route::post('logout', [ResidentController::class, 'logout']);

        Route::get('report/{id}', [ResidentController::class, 'report']);
        Route::post('reports/send', [ResidentController::class, 'report_send']);
        Route::get('reports', [ResidentController::class, 'reports']);
    });
});
