<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Enums\ResidentStatus;
use App\Http\Requests\Login;
use App\Http\Requests\Register;
use App\Models\Resident;
use App\Models\User;
use DB;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Password;
use Knu<PERSON>s\Scribe\Attributes\Authenticated;
use Knuckles\Scribe\Attributes\Unauthenticated;

class AuthController extends Controller
{
    /**
     * POST /api/register
     *
     * Register a new user and return an access token
     *
     * @bodyParam name string required The name of the user.
     * @bodyParam email string required The email of the user.
     * @bodyParam phone string required The phone of the user.
     * @bodyParam password string required The password of the user.
     *
     * @return JsonResponse
     *
     * @group Authentication
     */
    #[Unauthenticated]
    public function create(Register $request)
    {
        $validatedData = $request->validated();

        $user = Resident::create([
            'name' => $validatedData['name'],
            'email' => $validatedData['email'],
            'phone' => $validatedData['phone'],
            'password' => Hash::make($validatedData['password']),
            'status' => ResidentStatus::Inactive,
        ]);

        // send otp to user phone

    }

    /**
     * POST /api/login
     *
     * Authenticate user and return access token
     *
     * @bodyParam email string required The email of the user.
     * @bodyParam password string required The password of the user.
     *
     * @return JsonResponse
     *
     * @group Authentication
     */
    #[Unauthenticated]
    public function login(Login $request)
    {
        $loginData = $request->validated();

        $user = Resident::where('email', $loginData['email'])
            ->where('status', 'Active')
            ->first();
        if ($user) {
            if ($user->validateForApiPasswordGrant($loginData['password'])) {
                $accessToken = $user->createToken(
                    'userToken',
                    expiresAt: now()->addDays(1)
                )->plainTextToken;

                return response()->json([
                    'user' => $user,
                    'access_token' => $accessToken,
                ]);
            }
        }

        return response()->json(['message' => 'Invalid Credentials'], 401);
    }

    /**
     * GET /api/me
     *
     * Get the authenticated User
     *
     * @return User
     *
     * @group Authentication
     */
    #[Authenticated]
    public function me()
    {
        return auth()->user();
    }

    /**
     * @return JsonResponse
     *
     * @group Authentication
     */
    #[Authenticated]
    public function logout(Request $request)
    {
        $request
            ->user()
            ->currentAccessToken()
            ->delete();

        return response()->json(['message' => 'Logged out']);
    }

    /**
     * POST /api/forgot-password
     *
     * Send a password-reset link
     *
     * @bodyParam email string required User email.
     *
     * @return JsonResponse
     *
     * @group Authentication
     */
    #[Unauthenticated]
    public function forgotPassword(Request $request)
    {
        $request->validate(['email' => 'required|email']);

        $status = Password::sendResetLink($request->only('email'));

        return $status === Password::RESET_LINK_SENT
            ? response()->json(['status' => __($status)])
            : response()->json(['email' => __($status)], 400);
    }

    /**
     * POST /api/reset-password
     *
     * Reset user password
     *
     * @bodyParam email string required User email.
     * @bodyParam password string required User password.
     * @bodyParam password_confirmation string required User password confirmation.
     * @bodyParam token string required User token.
     *
     * @response JsonResponse{ "message": "Password reset successfully" }
     *
     * @group Authentication
     */
    #[Unauthenticated]
    public function resetPassword(Request $request)
    {
        $request->validate([
            'email' => 'required|email|exists:users,email',
            'password' => 'required|min:8|confirmed',
            'token' => 'required',
        ]);

        $query = DB::table('password_reset_tokens')
            ->select('*')
            ->where('email', $request->email);

        $token = $query->first();

        if ($token && Hash::check($request->token, $token->token)) {
            $user = Resident::where('email', $request->email)->first();

            if ($user) {
                $user->password = Hash::make($request->password);
                $user->save();
                $query->delete();

                return response()->json([
                    'message' => 'Password reset successfully',
                ]);
            }
        }

        return response()->json(['message' => 'Invalid token'], 400);
    }
}
